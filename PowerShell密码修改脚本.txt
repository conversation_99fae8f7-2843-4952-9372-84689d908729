# 密码修改PowerShell脚本
# 从工具箱项目中提取的密码修改功能

## 域用户密码修改脚本
```powershell
try {
    Add-Type -AssemblyName System.DirectoryServices.AccountManagement
    
    # 解码密码
    $currentPasswordBytes = [System.Convert]::FromBase64String('{currentPasswordBase64}')
    $currentPassword = [System.Text.Encoding]::UTF8.GetString($currentPasswordBytes)
    
    $newPasswordBytes = [System.Convert]::FromBase64String('{newPasswordBase64}')
    $newPassword = [System.Text.Encoding]::UTF8.GetString($newPasswordBytes)
    
    # 验证当前密码（域用户）
    $pc = New-Object System.DirectoryServices.AccountManagement.PrincipalContext([System.DirectoryServices.AccountManagement.ContextType]::Domain, '{domainName}')
    $isValid = $pc.ValidateCredentials('{currentUser}', $currentPassword)
    
    if (-not $isValid) {
        Write-Output 'INVALID_CURRENT_PASSWORD'
        exit 1
    }
    
    # 修改域用户密码
    $user = [System.DirectoryServices.AccountManagement.UserPrincipal]::FindByIdentity($pc, [System.DirectoryServices.AccountManagement.IdentityType]::SamAccountName, '{currentUser}')
    if ($user -eq $null) {
        Write-Output 'ERROR: 无法找到域用户'
        exit 1
    }
    
    $user.ChangePassword($currentPassword, $newPassword)
    $user.Save()
    
    Write-Output 'SUCCESS'
    exit 0
}
catch {
    $errorMsg = $_.Exception.Message
    if ($errorMsg -like '*拒绝访问*' -or $errorMsg -like '*Access*denied*') {
        Write-Output "ERROR: 权限不足，无法修改域用户密码。请确保：1) 当前密码正确 2) 新密码符合域密码策略 3) 账户未被锁定"
    }
    elseif ($errorMsg -like '*密码*策略*' -or $errorMsg -like '*password*policy*') {
        Write-Output "ERROR: 新密码不符合域密码策略要求，请检查密码复杂度、长度和历史记录要求"
    }
    else {
        Write-Output "ERROR: $errorMsg"
    }
    exit 1
}
```

## 本地用户密码修改脚本
```powershell
try {
    Add-Type -AssemblyName System.DirectoryServices.AccountManagement
    
    # 解码密码
    $currentPasswordBytes = [System.Convert]::FromBase64String('{currentPasswordBase64}')
    $currentPassword = [System.Text.Encoding]::UTF8.GetString($currentPasswordBytes)
    
    $newPasswordBytes = [System.Convert]::FromBase64String('{newPasswordBase64}')
    $newPassword = [System.Text.Encoding]::UTF8.GetString($newPasswordBytes)
    
    # 验证当前密码（本地用户）
    $pc = New-Object System.DirectoryServices.AccountManagement.PrincipalContext([System.DirectoryServices.AccountManagement.ContextType]::Machine)
    $isValid = $pc.ValidateCredentials('{currentUser}', $currentPassword)
    
    if (-not $isValid) {
        Write-Output 'INVALID_CURRENT_PASSWORD'
        exit 1
    }
    
    # 修改本地用户密码
    $user = [ADSI]'WinNT://./{currentUser},user'
    $user.SetPassword($newPassword)
    $user.SetInfo()
    
    Write-Output 'SUCCESS'
    exit 0
}
catch {
    Write-Output "ERROR: $($_.Exception.Message)"
    exit 1
}
```

## 使用说明

### 变量替换
在实际使用时，需要将以下占位符替换为实际值：
- {currentPasswordBase64}: 当前密码的Base64编码
- {newPasswordBase64}: 新密码的Base64编码
- {domainName}: 域名（仅域用户脚本需要）
- {currentUser}: 当前用户名

### 返回值说明
- SUCCESS: 密码修改成功
- INVALID_CURRENT_PASSWORD: 当前密码验证失败
- ERROR: [错误信息]: 操作失败，包含具体错误信息

### 执行要求
- 需要管理员权限
- PowerShell执行策略需要允许脚本执行
- 域用户需要域控制器连接正常

### Base64编码示例（C#）
```csharp
string currentPasswordBase64 = Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes(currentPassword));
string newPasswordBase64 = Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes(newPassword));
```
