#include <GUIConstantsEx.au3>
#include <EditConstants.au3>
#include <WindowsConstants.au3>
#include <MsgBoxConstants.au3>
#include <ComboConstants.au3>
#include <Constants.au3>

; 全局变量
Global $hGUI, $hUsername, $hCurrentPassword, $hNewPassword, $hConfirmPassword
Global $hChangeButton, $hExitButton
Global Const $DOMAIN_NAME = "nvtpower.com"
Global Const $LOG_FILE = @ScriptDir & "\password_change_log.txt"

; 创建GUI
$hGUI = GUICreate("域用户密码修改工具", 400, 280, -1, -1)

; 用户名显示
GUICtrlCreateLabel("用户名:", 20, 30, 80, 20)
$hUsername = GUICtrlCreateInput(@UserName, 110, 28, 200, 25, $ES_READONLY)

; 当前密码输入
GUICtrlCreateLabel("当前密码:", 20, 65, 80, 20)
$hCurrentPassword = GUICtrlCreateInput("", 110, 63, 200, 25, $ES_PASSWORD)

; 新密码输入
GUICtrlCreateLabel("新密码:", 20, 100, 80, 20)
$hNewPassword = GUICtrlCreateInput("", 110, 98, 200, 25, $ES_PASSWORD)

; 确认新密码输入
GUICtrlCreateLabel("确认新密码:", 20, 135, 80, 20)
$hConfirmPassword = GUICtrlCreateInput("", 110, 133, 200, 25, $ES_PASSWORD)

; 按钮
$hChangeButton = GUICtrlCreateButton("修改密码", 110, 180, 80, 30)
$hExitButton = GUICtrlCreateButton("退出", 210, 180, 80, 30)

; 状态信息
GUICtrlCreateLabel("状态信息:", 20, 225, 80, 20)
Global $hStatusLabel = GUICtrlCreateLabel("", 20, 245, 360, 20)

GUISetState(@SW_SHOW)

; 事件循环
While 1
    $nMsg = GUIGetMsg()
    Switch $nMsg
        Case $GUI_EVENT_CLOSE, $hExitButton
            Exit
        Case $hChangeButton
            OnChangePassword()
    EndSwitch
WEnd

; 修改密码事件
Func OnChangePassword()
    ; 获取输入值
    Local $sUsername = GUICtrlRead($hUsername)
    Local $sCurrentPassword = GUICtrlRead($hCurrentPassword)
    Local $sNewPassword = GUICtrlRead($hNewPassword)
    Local $sConfirmPassword = GUICtrlRead($hConfirmPassword)

    ; 验证输入
    If $sUsername = "" Then
        UpdateStatus("错误: 请输入用户名")
        Return
    EndIf

    If $sCurrentPassword = "" Then
        UpdateStatus("错误: 请输入当前密码")
        Return
    EndIf

    If $sNewPassword = "" Then
        UpdateStatus("错误: 请输入新密码")
        Return
    EndIf

    If $sNewPassword <> $sConfirmPassword Then
        UpdateStatus("错误: 新密码和确认密码不匹配")
        Return
    EndIf

    ; 执行密码修改
    UpdateStatus("正在修改密码...")
    WriteLog("开始为用户 " & $sUsername & " 修改域密码")

    Local $bResult = ChangeDomainUserPassword($DOMAIN_NAME, $sUsername, $sCurrentPassword, $sNewPassword)

    If $bResult Then
        UpdateStatus("密码修改成功!")
        ; 清空密码字段
        GUICtrlSetData($hCurrentPassword, "")
        GUICtrlSetData($hNewPassword, "")
        GUICtrlSetData($hConfirmPassword, "")
    EndIf
EndFunc

; 更新状态信息
Func UpdateStatus($sMessage)
    GUICtrlSetData($hStatusLabel, $sMessage)
    ; 记录日志
    WriteLog($sMessage)
EndFunc

; 写入日志
Func WriteLog($sMessage)
    Local $sTimestamp = @YEAR & "-" & @MON & "-" & @MDAY & " " & @HOUR & ":" & @MIN & ":" & @SEC
    Local $sLogEntry = "[" & $sTimestamp & "] " & $sMessage & @CRLF
    Local $hFile = FileOpen($LOG_FILE, 1) ; 追加模式
    If $hFile <> -1 Then
        FileWrite($hFile, $sLogEntry)
        FileClose($hFile)
    EndIf
EndFunc

; 修改域用户密码
Func ChangeDomainUserPassword($sDomainName, $sUsername, $sCurrentPassword, $sNewPassword)
    ; Base64编码密码
    Local $sCurrentPasswordBase64 = Base64Encode($sCurrentPassword)
    Local $sNewPasswordBase64 = Base64Encode($sNewPassword)
    
    ; 创建PowerShell脚本内容
    Local $sPSScript = ""
    $sPSScript &= "try {" & @CRLF
    $sPSScript &= "    Add-Type -AssemblyName System.DirectoryServices.AccountManagement" & @CRLF
    $sPSScript &= "    $currentPasswordBase64 = '" & $sCurrentPasswordBase64 & "'" & @CRLF
    $sPSScript &= "    $newPasswordBase64 = '" & $sNewPasswordBase64 & "'" & @CRLF
    $sPSScript &= "    $currentPasswordBytes = [System.Convert]::FromBase64String($currentPasswordBase64)" & @CRLF
    $sPSScript &= "    $currentPassword = [System.Text.Encoding]::UTF8.GetString($currentPasswordBytes)" & @CRLF
    $sPSScript &= "    $newPasswordBytes = [System.Convert]::FromBase64String($newPasswordBase64)" & @CRLF
    $sPSScript &= "    $newPassword = [System.Text.Encoding]::UTF8.GetString($newPasswordBytes)" & @CRLF
    $sPSScript &= "    $pc = New-Object System.DirectoryServices.AccountManagement.PrincipalContext([System.DirectoryServices.AccountManagement.ContextType]::Domain, '" & $sDomainName & "')" & @CRLF
    $sPSScript &= "    $isValid = $pc.ValidateCredentials('" & $sUsername & "', $currentPassword)" & @CRLF
    $sPSScript &= "    if (-not $isValid) {" & @CRLF
    $sPSScript &= "        Write-Output 'INVALID_CURRENT_PASSWORD'" & @CRLF
    $sPSScript &= "        exit 1" & @CRLF
    $sPSScript &= "    }" & @CRLF
    $sPSScript &= "    $user = [System.DirectoryServices.AccountManagement.UserPrincipal]::FindByIdentity($pc, [System.DirectoryServices.AccountManagement.IdentityType]::SamAccountName, '" & $sUsername & "')" & @CRLF
    $sPSScript &= "    if ($user -eq $null) {" & @CRLF
    $sPSScript &= "        Write-Output 'ERROR: User not found in domain'" & @CRLF
    $sPSScript &= "        exit 1" & @CRLF
    $sPSScript &= "    }" & @CRLF
    $sPSScript &= "    $user.ChangePassword($currentPassword, $newPassword)" & @CRLF
    $sPSScript &= "    $user.Save()" & @CRLF
    $sPSScript &= "    Write-Output 'SUCCESS'" & @CRLF
    $sPSScript &= "    exit 0" & @CRLF
    $sPSScript &= "}" & @CRLF
    $sPSScript &= "catch {" & @CRLF
    $sPSScript &= "    $errorMsg = $_.Exception.Message" & @CRLF
    $sPSScript &= "    if ($errorMsg -like '*Access*denied*' -or $errorMsg -like '*access*denied*') {" & @CRLF
    $sPSScript &= "        Write-Output 'ERROR: Access denied - insufficient permissions'" & @CRLF
    $sPSScript &= "    }" & @CRLF
    $sPSScript &= "    elseif ($errorMsg -like '*password*policy*' -or $errorMsg -like '*Password*policy*') {" & @CRLF
    $sPSScript &= "        Write-Output 'ERROR: New password does not meet domain policy requirements'" & @CRLF
    $sPSScript &= "    }" & @CRLF
    $sPSScript &= "    else {" & @CRLF
    $sPSScript &= "        Write-Output ('ERROR: ' + $errorMsg)" & @CRLF
    $sPSScript &= "    }" & @CRLF
    $sPSScript &= "    exit 1" & @CRLF
    $sPSScript &= "}" & @CRLF

    ; 记录生成的PowerShell脚本到日志
    WriteLog("生成的PowerShell脚本:")
    WriteLog($sPSScript)
    WriteLog("--- 脚本结束 ---")

    Return ExecutePowerShellScript($sPSScript)
EndFunc



; 执行PowerShell脚本
Func ExecutePowerShellScript($sPSScript)
    ; 将脚本写入临时文件
    Local $sTempFile = @TempDir & "\password_change_" & @YEAR & @MON & @MDAY & @HOUR & @MIN & @SEC & ".ps1"
    Local $hFile = FileOpen($sTempFile, 2)
    If $hFile = -1 Then
        UpdateStatus("错误: 无法创建临时脚本文件")
        Return False
    EndIf

    ; 使用UTF-8编码写入文件
    FileWrite($hFile, Chr(239) & Chr(187) & Chr(191)) ; UTF-8 BOM
    FileWrite($hFile, $sPSScript)
    FileClose($hFile)

    ; 执行PowerShell脚本
    Local $sCommand = 'powershell.exe -ExecutionPolicy Bypass -NoProfile -WindowStyle Hidden -File "' & $sTempFile & '"'
    Local $iPID = Run($sCommand, "", @SW_HIDE, $STDERR_CHILD + $STDOUT_CHILD)

    ; 等待执行完成
    ProcessWaitClose($iPID)

    ; 读取输出
    Local $sOutput = StdoutRead($iPID)
    Local $sError = StderrRead($iPID)

    ; 删除临时文件
    FileDelete($sTempFile)

    ; 记录详细的执行结果到日志
    WriteLog("PowerShell执行结果:")
    WriteLog("STDOUT: " & $sOutput)
    WriteLog("STDERR: " & $sError)

    ; 处理结果
    If StringInStr($sOutput, "SUCCESS") Then
        WriteLog("密码修改成功")
        Return True
    ElseIf StringInStr($sOutput, "INVALID_CURRENT_PASSWORD") Then
        UpdateStatus("错误: 当前密码验证失败")
        Return False
    ElseIf StringInStr($sOutput, "ERROR:") Then
        Local $sErrorMsg = StringStripWS(StringReplace($sOutput, "ERROR:", ""), 3)
        ; 将英文错误信息转换为中文
        If StringInStr($sErrorMsg, "User not found in domain") Then
            $sErrorMsg = "无法找到域用户"
        ElseIf StringInStr($sErrorMsg, "Access denied") Then
            $sErrorMsg = "权限不足，无法修改域用户密码"
        ElseIf StringInStr($sErrorMsg, "password does not meet domain policy") Then
            $sErrorMsg = "新密码不符合域密码策略要求"
        EndIf
        UpdateStatus("错误: " & $sErrorMsg)
        Return False
    Else
        ; 处理其他错误情况
        Local $sFullError = ""
        If $sError <> "" Then
            $sFullError = $sError
        EndIf
        If $sOutput <> "" And Not StringInStr($sOutput, "SUCCESS") Then
            If $sFullError <> "" Then
                $sFullError &= " | " & $sOutput
            Else
                $sFullError = $sOutput
            EndIf
        EndIf

        If $sFullError = "" Then
            UpdateStatus("错误: PowerShell脚本执行失败，未返回任何信息")
        Else
            ; 记录完整错误信息到日志
            WriteLog("完整错误信息: " & $sFullError)
            ; 清理错误信息，只显示关键部分
            $sFullError = StringRegExpReplace($sFullError, "所在位置.*", "")
            $sFullError = StringRegExpReplace($sFullError, "At line:.*", "")
            $sFullError = StringStripWS($sFullError, 3)
            UpdateStatus("错误: " & $sFullError)
        EndIf
        Return False
    EndIf
EndFunc

; Base64编码函数
Func Base64Encode($sData)
    Local $oXML = ObjCreate("MSXML2.DOMDocument")
    Local $oNode = $oXML.createElement("base64")
    $oNode.dataType = "bin.base64"
    $oNode.nodeTypedValue = Binary($sData)
    Local $sResult = $oNode.text
    ; 清理Base64字符串，移除换行符和空格
    $sResult = StringReplace($sResult, @CRLF, "")
    $sResult = StringReplace($sResult, @LF, "")
    $sResult = StringReplace($sResult, @CR, "")
    $sResult = StringReplace($sResult, " ", "")
    Return $sResult
EndFunc

; Base64解码函数（备用）
Func Base64Decode($sData)
    Local $oXML = ObjCreate("MSXML2.DOMDocument")
    Local $oNode = $oXML.createElement("base64")
    $oNode.dataType = "bin.base64"
    $oNode.text = $sData
    Return BinaryToString($oNode.nodeTypedValue)
EndFunc
