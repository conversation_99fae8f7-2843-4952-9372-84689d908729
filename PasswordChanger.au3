#include <GUIConstantsEx.au3>
#include <EditConstants.au3>
#include <WindowsConstants.au3>
#include <MsgBoxConstants.au3>
#include <ComboConstants.au3>
#include <Constants.au3>

; 全局变量
Global $hGUI, $hUsername, $hCurrentPassword, $hNewPassword, $hConfirmPassword
Global $hChangeButton, $hExitButton
Global Const $DOMAIN_NAME = "nvtpower.com"

; 创建GUI
$hGUI = GUICreate("域用户密码修改工具", 400, 280, -1, -1)

; 用户名显示
GUICtrlCreateLabel("用户名:", 20, 30, 80, 20)
$hUsername = GUICtrlCreateInput(@UserName, 110, 28, 200, 25, $ES_READONLY)

; 当前密码输入
GUICtrlCreateLabel("当前密码:", 20, 65, 80, 20)
$hCurrentPassword = GUICtrlCreateInput("", 110, 63, 200, 25, $ES_PASSWORD)

; 新密码输入
GUICtrlCreateLabel("新密码:", 20, 100, 80, 20)
$hNewPassword = GUICtrlCreateInput("", 110, 98, 200, 25, $ES_PASSWORD)

; 确认新密码输入
GUICtrlCreateLabel("确认新密码:", 20, 135, 80, 20)
$hConfirmPassword = GUICtrlCreateInput("", 110, 133, 200, 25, $ES_PASSWORD)

; 按钮
$hChangeButton = GUICtrlCreateButton("修改密码", 110, 180, 80, 30)
$hExitButton = GUICtrlCreateButton("退出", 210, 180, 80, 30)

; 状态信息
GUICtrlCreateLabel("状态信息:", 20, 225, 80, 20)
Global $hStatusLabel = GUICtrlCreateLabel("", 20, 245, 360, 20)

GUISetState(@SW_SHOW)

; 事件循环
While 1
    $nMsg = GUIGetMsg()
    Switch $nMsg
        Case $GUI_EVENT_CLOSE, $hExitButton
            Exit
        Case $hChangeButton
            OnChangePassword()
    EndSwitch
WEnd

; 修改密码事件
Func OnChangePassword()
    ; 获取输入值
    Local $sUsername = GUICtrlRead($hUsername)
    Local $sCurrentPassword = GUICtrlRead($hCurrentPassword)
    Local $sNewPassword = GUICtrlRead($hNewPassword)
    Local $sConfirmPassword = GUICtrlRead($hConfirmPassword)

    ; 验证输入
    If $sUsername = "" Then
        UpdateStatus("错误: 请输入用户名")
        Return
    EndIf

    If $sCurrentPassword = "" Then
        UpdateStatus("错误: 请输入当前密码")
        Return
    EndIf

    If $sNewPassword = "" Then
        UpdateStatus("错误: 请输入新密码")
        Return
    EndIf

    If $sNewPassword <> $sConfirmPassword Then
        UpdateStatus("错误: 新密码和确认密码不匹配")
        Return
    EndIf

    ; 执行密码修改
    UpdateStatus("正在修改密码...")

    Local $bResult = ChangeDomainUserPassword($DOMAIN_NAME, $sUsername, $sCurrentPassword, $sNewPassword)

    If $bResult Then
        UpdateStatus("密码修改成功!")
        ; 清空密码字段
        GUICtrlSetData($hCurrentPassword, "")
        GUICtrlSetData($hNewPassword, "")
        GUICtrlSetData($hConfirmPassword, "")
    EndIf
EndFunc

; 更新状态信息
Func UpdateStatus($sMessage)
    GUICtrlSetData($hStatusLabel, $sMessage)
EndFunc

; 修改域用户密码
Func ChangeDomainUserPassword($sDomainName, $sUsername, $sCurrentPassword, $sNewPassword)
    ; Base64编码密码
    Local $sCurrentPasswordBase64 = Base64Encode($sCurrentPassword)
    Local $sNewPasswordBase64 = Base64Encode($sNewPassword)
    
    ; 创建PowerShell脚本内容
    Local $sPSScript = 'try {' & @CRLF & _
        '    Add-Type -AssemblyName System.DirectoryServices.AccountManagement' & @CRLF & _
        '    $currentPasswordBytes = [System.Convert]::FromBase64String("' & $sCurrentPasswordBase64 & '")' & @CRLF & _
        '    $currentPassword = [System.Text.Encoding]::UTF8.GetString($currentPasswordBytes)' & @CRLF & _
        '    $newPasswordBytes = [System.Convert]::FromBase64String("' & $sNewPasswordBase64 & '")' & @CRLF & _
        '    $newPassword = [System.Text.Encoding]::UTF8.GetString($newPasswordBytes)' & @CRLF & _
        '    $pc = New-Object System.DirectoryServices.AccountManagement.PrincipalContext([System.DirectoryServices.AccountManagement.ContextType]::Domain, "' & $sDomainName & '")' & @CRLF & _
        '    $isValid = $pc.ValidateCredentials("' & $sUsername & '", $currentPassword)' & @CRLF & _
        '    if (-not $isValid) {' & @CRLF & _
        '        Write-Output "INVALID_CURRENT_PASSWORD"' & @CRLF & _
        '        exit 1' & @CRLF & _
        '    }' & @CRLF & _
        '    $user = [System.DirectoryServices.AccountManagement.UserPrincipal]::FindByIdentity($pc, [System.DirectoryServices.AccountManagement.IdentityType]::SamAccountName, "' & $sUsername & '")' & @CRLF & _
        '    if ($user -eq $null) {' & @CRLF & _
        '        Write-Output "ERROR: 无法找到域用户"' & @CRLF & _
        '        exit 1' & @CRLF & _
        '    }' & @CRLF & _
        '    $user.ChangePassword($currentPassword, $newPassword)' & @CRLF & _
        '    $user.Save()' & @CRLF & _
        '    Write-Output "SUCCESS"' & @CRLF & _
        '    exit 0' & @CRLF & _
        '}' & @CRLF & _
        'catch {' & @CRLF & _
        '    $errorMsg = $_.Exception.Message' & @CRLF & _
        '    if ($errorMsg -like "*拒绝访问*" -or $errorMsg -like "*Access*denied*") {' & @CRLF & _
        '        Write-Output "ERROR: 权限不足，无法修改域用户密码"' & @CRLF & _
        '    }' & @CRLF & _
        '    elseif ($errorMsg -like "*密码*策略*" -or $errorMsg -like "*password*policy*") {' & @CRLF & _
        '        Write-Output "ERROR: 新密码不符合域密码策略要求"' & @CRLF & _
        '    }' & @CRLF & _
        '    else {' & @CRLF & _
        '        Write-Output "ERROR: $errorMsg"' & @CRLF & _
        '    }' & @CRLF & _
        '    exit 1' & @CRLF & _
        '}'
    
    Return ExecutePowerShellScript($sPSScript)
EndFunc



; 执行PowerShell脚本
Func ExecutePowerShellScript($sPSScript)
    ; 将脚本写入临时文件
    Local $sTempFile = @TempDir & "\password_change_" & @YEAR & @MON & @MDAY & @HOUR & @MIN & @SEC & ".ps1"
    Local $hFile = FileOpen($sTempFile, 2)
    If $hFile = -1 Then
        UpdateStatus("错误: 无法创建临时脚本文件")
        Return False
    EndIf

    FileWrite($hFile, $sPSScript)
    FileClose($hFile)

    ; 执行PowerShell脚本
    Local $sCommand = 'powershell.exe -ExecutionPolicy Bypass -NoProfile -WindowStyle Hidden -File "' & $sTempFile & '"'
    Local $iPID = Run($sCommand, "", @SW_HIDE, $STDERR_CHILD + $STDOUT_CHILD)

    ; 等待执行完成
    ProcessWaitClose($iPID)

    ; 读取输出
    Local $sOutput = StdoutRead($iPID)
    Local $sError = StderrRead($iPID)

    ; 删除临时文件
    FileDelete($sTempFile)

    ; 处理结果
    If StringInStr($sOutput, "SUCCESS") Then
        Return True
    ElseIf StringInStr($sOutput, "INVALID_CURRENT_PASSWORD") Then
        UpdateStatus("错误: 当前密码验证失败")
        Return False
    ElseIf StringInStr($sOutput, "ERROR:") Then
        Local $sErrorMsg = StringStripWS(StringReplace($sOutput, "ERROR:", ""), 3)
        UpdateStatus("错误: " & $sErrorMsg)
        Return False
    Else
        If $sError <> "" Then
            UpdateStatus("错误: " & $sError)
        Else
            UpdateStatus("错误: 未知错误 - " & $sOutput)
        EndIf
        Return False
    EndIf
EndFunc

; Base64编码函数
Func Base64Encode($sData)
    Local $oXML = ObjCreate("MSXML2.DOMDocument")
    Local $oNode = $oXML.createElement("base64")
    $oNode.dataType = "bin.base64"
    $oNode.nodeTypedValue = Binary($sData)
    Return $oNode.text
EndFunc

; Base64解码函数（备用）
Func Base64Decode($sData)
    Local $oXML = ObjCreate("MSXML2.DOMDocument")
    Local $oNode = $oXML.createElement("base64")
    $oNode.dataType = "bin.base64"
    $oNode.text = $sData
    Return BinaryToString($oNode.nodeTypedValue)
EndFunc
